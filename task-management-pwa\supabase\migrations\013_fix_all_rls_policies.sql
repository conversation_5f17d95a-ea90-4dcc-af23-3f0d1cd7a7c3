-- Fix ALL RLS Policies to Use Users Table Role Instead of auth.role()
-- This migration comprehensively fixes all RLS policies to use the role from public.users table
-- and eliminates infinite recursion issues
--
-- IMPORTANT: This migration will drop ALL existing policies and recreate them
-- Make sure to backup your database before running this migration

-- ============================================================================
-- STEP 1: Drop ALL existing policies first (before dropping functions)
-- ============================================================================

-- Users table policies
DROP POLICY IF EXISTS "Users can view approved users" ON public.users;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;
DROP POLICY IF EXISTS "Admins have full access to users" ON public.users;
DROP POLICY IF EXISTS "users_select_approved" ON public.users;
DROP POLICY IF EXISTS "users_select_own" ON public.users;
DROP POLICY IF EXISTS "users_update_own" ON public.users;
DROP POLICY IF EXISTS "users_insert_own" ON public.users;
DROP POLICY IF EXISTS "users_admin_all" ON public.users;

-- Tasks table policies
DROP POLICY IF EXISTS "Users can view assigned tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can create tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can update their assigned tasks" ON public.tasks;
DROP POLICY IF EXISTS "Admins can manage all tasks" ON public.tasks;
DROP POLICY IF EXISTS "Admins have full access to tasks" ON public.tasks;
DROP POLICY IF EXISTS "tasks_select_assigned" ON public.tasks;
DROP POLICY IF EXISTS "tasks_insert_own" ON public.tasks;
DROP POLICY IF EXISTS "tasks_update_involved" ON public.tasks;
DROP POLICY IF EXISTS "tasks_delete_creator" ON public.tasks;

-- Notifications table policies
DROP POLICY IF EXISTS "Users can view their notifications" ON public.notifications;
DROP POLICY IF EXISTS "System can create notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their notifications" ON public.notifications;
DROP POLICY IF EXISTS "Admins have full access to notifications" ON public.notifications;
DROP POLICY IF EXISTS "notifications_select_own" ON public.notifications;
DROP POLICY IF EXISTS "notifications_insert_system" ON public.notifications;
DROP POLICY IF EXISTS "notifications_update_own" ON public.notifications;

-- Task sessions table policies
DROP POLICY IF EXISTS "Users can manage their own sessions" ON public.task_sessions;
DROP POLICY IF EXISTS "Admins can view all sessions" ON public.task_sessions;
DROP POLICY IF EXISTS "Admins have full access to task_sessions" ON public.task_sessions;
DROP POLICY IF EXISTS "task_sessions_own" ON public.task_sessions;

-- Recurring task instances policies
DROP POLICY IF EXISTS "Users can view their recurring task instances" ON public.recurring_task_instances;
DROP POLICY IF EXISTS "System can manage recurring task instances" ON public.recurring_task_instances;
DROP POLICY IF EXISTS "Admins have full access to recurring_task_instances" ON public.recurring_task_instances;
DROP POLICY IF EXISTS "recurring_instances_select" ON public.recurring_task_instances;
DROP POLICY IF EXISTS "recurring_instances_system" ON public.recurring_task_instances;

-- Timezone config policies
DROP POLICY IF EXISTS "Allow authenticated users to read timezone config" ON public.timezone_config;
DROP POLICY IF EXISTS "Allow admins to update timezone config" ON public.timezone_config;
DROP POLICY IF EXISTS "Admins have full access to timezone_config" ON public.timezone_config;
DROP POLICY IF EXISTS "timezone_config_select" ON public.timezone_config;
DROP POLICY IF EXISTS "timezone_config_admin" ON public.timezone_config;

-- Supported timezones policies
DROP POLICY IF EXISTS "Allow authenticated users to read supported timezones" ON public.supported_timezones;
DROP POLICY IF EXISTS "Admins have full access to supported_timezones" ON public.supported_timezones;
DROP POLICY IF EXISTS "supported_timezones_select" ON public.supported_timezones;
DROP POLICY IF EXISTS "supported_timezones_admin" ON public.supported_timezones;

-- Audit log policies
DROP POLICY IF EXISTS "Users can read their own audit records" ON public.audit_log;
DROP POLICY IF EXISTS "Admins can read all audit records" ON public.audit_log;
DROP POLICY IF EXISTS "System can insert audit records" ON public.audit_log;
DROP POLICY IF EXISTS "Admins have full access to audit_log" ON public.audit_log;
DROP POLICY IF EXISTS "audit_log_select_own" ON public.audit_log;
DROP POLICY IF EXISTS "audit_log_insert_system" ON public.audit_log;

-- Additional cleanup for any other policies that might exist
DO $$
DECLARE
    r RECORD;
BEGIN
    -- Drop all policies that use the is_admin function to avoid dependency issues
    FOR r IN
        SELECT schemaname, tablename, policyname
        FROM pg_policies
        WHERE schemaname = 'public'
        AND policyname NOT IN (
            'users_select_approved', 'users_select_own', 'users_update_own', 'users_insert_own', 'users_admin_all',
            'tasks_select_assigned', 'tasks_insert_own', 'tasks_update_involved', 'tasks_delete_creator',
            'notifications_select_own', 'notifications_insert_system', 'notifications_update_own',
            'task_sessions_own', 'recurring_instances_select', 'recurring_instances_system',
            'timezone_config_select', 'timezone_config_admin', 'supported_timezones_select', 'supported_timezones_admin',
            'audit_log_select_own', 'audit_log_insert_system'
        )
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', r.policyname, r.schemaname, r.tablename);
    END LOOP;
END $$;

-- ============================================================================
-- STEP 2: Drop existing functions (now that policies are gone)
-- ============================================================================

-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS public.is_admin() CASCADE;
DROP FUNCTION IF EXISTS public.is_owner(UUID) CASCADE;

-- ============================================================================
-- STEP 3: Create helper functions with proper security
-- ============================================================================

-- Create admin check function (security definer to avoid recursion)
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- This function runs with elevated privileges and bypasses RLS
  -- It's safe from infinite recursion because it uses SECURITY DEFINER
  RETURN EXISTS (
    SELECT 1
    FROM public.users
    WHERE id = auth.uid()
    AND role = 'admin'
    AND approved = true
  );
END;
$$;

-- Create ownership check function
CREATE OR REPLACE FUNCTION public.is_owner(record_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN record_user_id = auth.uid();
END;
$$;

-- Create function to prevent users from changing their own role/approval
CREATE OR REPLACE FUNCTION public.prevent_self_privilege_escalation()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Allow admins to change anything
  IF public.is_admin() THEN
    RETURN NEW;
  END IF;

  -- For non-admins, prevent changing role and approval status
  IF OLD.role != NEW.role THEN
    RAISE EXCEPTION 'Users cannot change their own role';
  END IF;

  IF OLD.approved != NEW.approved THEN
    RAISE EXCEPTION 'Users cannot change their own approval status';
  END IF;

  RETURN NEW;
END;
$$;

-- Create trigger to enforce self-privilege escalation prevention
DROP TRIGGER IF EXISTS prevent_self_privilege_escalation_trigger ON public.users;
CREATE TRIGGER prevent_self_privilege_escalation_trigger
  BEFORE UPDATE ON public.users
  FOR EACH ROW
  WHEN (OLD.id = auth.uid())  -- Only for users updating their own record
  EXECUTE FUNCTION public.prevent_self_privilege_escalation();

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_owner(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.prevent_self_privilege_escalation() TO authenticated;

-- ============================================================================
-- STEP 4: Create NEW optimized RLS policies using helper functions
-- ============================================================================

-- Safety check: Ensure required tables exist
DO $$
BEGIN
    -- Check if users table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') THEN
        RAISE EXCEPTION 'Users table does not exist. Please run previous migrations first.';
    END IF;

    -- Check if tasks table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks') THEN
        RAISE NOTICE 'Tasks table does not exist. Skipping task policies.';
    END IF;
END $$;

-- USERS TABLE POLICIES
-- =====================

-- Users can view approved users (public directory)
CREATE POLICY "users_select_approved" ON public.users
  FOR SELECT TO authenticated, anon
  USING (approved = true);

-- Users can view their own profile
CREATE POLICY "users_select_own" ON public.users
  FOR SELECT TO authenticated
  USING (public.is_owner(id));

-- Users can update their own profile (limited fields)
CREATE POLICY "users_update_own" ON public.users
  FOR UPDATE TO authenticated
  USING (public.is_owner(id))
  WITH CHECK (public.is_owner(id));

-- Users can insert their own profile (OAuth registration)
CREATE POLICY "users_insert_own" ON public.users
  FOR INSERT TO authenticated
  WITH CHECK (public.is_owner(id));

-- Admins can manage all users
CREATE POLICY "users_admin_all" ON public.users
  FOR ALL TO authenticated
  USING (public.is_admin())
  WITH CHECK (public.is_admin());

-- TASKS TABLE POLICIES
-- ====================

-- Users can view tasks assigned to them or created by them
CREATE POLICY "tasks_select_assigned" ON public.tasks
  FOR SELECT TO authenticated
  USING (
    public.is_owner(assigned_to) 
    OR public.is_owner(assigned_by)
    OR public.is_admin()
  );

-- Users can create tasks (assigned_by must be themselves)
CREATE POLICY "tasks_insert_own" ON public.tasks
  FOR INSERT TO authenticated
  WITH CHECK (
    public.is_owner(assigned_by)
    OR public.is_admin()
  );

-- Users can update tasks they're involved with
CREATE POLICY "tasks_update_involved" ON public.tasks
  FOR UPDATE TO authenticated
  USING (
    public.is_owner(assigned_to) 
    OR public.is_owner(assigned_by)
    OR public.is_admin()
  )
  WITH CHECK (
    public.is_owner(assigned_to) 
    OR public.is_owner(assigned_by)
    OR public.is_admin()
  );

-- Users can delete tasks they created (or admins can delete any)
CREATE POLICY "tasks_delete_creator" ON public.tasks
  FOR DELETE TO authenticated
  USING (
    public.is_owner(assigned_by)
    OR public.is_admin()
  );

-- NOTIFICATIONS TABLE POLICIES
-- ============================

-- Users can view their own notifications
CREATE POLICY "notifications_select_own" ON public.notifications
  FOR SELECT TO authenticated
  USING (
    public.is_owner(user_id)
    OR public.is_admin()
  );

-- System/triggers can create notifications (SECURITY DEFINER functions)
CREATE POLICY "notifications_insert_system" ON public.notifications
  FOR INSERT TO authenticated
  WITH CHECK (true);  -- Controlled by SECURITY DEFINER functions

-- Users can update their own notifications (mark as read)
CREATE POLICY "notifications_update_own" ON public.notifications
  FOR UPDATE TO authenticated
  USING (
    public.is_owner(user_id)
    OR public.is_admin()
  )
  WITH CHECK (
    public.is_owner(user_id)
    OR public.is_admin()
  );

-- TASK SESSIONS TABLE POLICIES
-- ============================

-- Users can manage their own sessions
CREATE POLICY "task_sessions_own" ON public.task_sessions
  FOR ALL TO authenticated
  USING (
    public.is_owner(user_id)
    OR public.is_admin()
  )
  WITH CHECK (
    public.is_owner(user_id)
    OR public.is_admin()
  );

-- RECURRING TASK INSTANCES POLICIES (CONDITIONAL)
-- ================================================

-- Only create policies if table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'recurring_task_instances') THEN
        -- Users can view recurring task instances for their tasks
        EXECUTE 'CREATE POLICY "recurring_instances_select" ON public.recurring_task_instances
          FOR SELECT TO authenticated
          USING (
            EXISTS (
              SELECT 1 FROM public.tasks
              WHERE id = parent_task_id
              AND (
                public.is_owner(assigned_to)
                OR public.is_owner(assigned_by)
              )
            )
            OR public.is_admin()
          )';

        -- System can manage recurring task instances
        EXECUTE 'CREATE POLICY "recurring_instances_system" ON public.recurring_task_instances
          FOR ALL TO service_role';
    END IF;
END $$;

-- TIMEZONE CONFIGURATION POLICIES (CONDITIONAL)
-- ==============================================

-- Only create policies if table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'timezone_config') THEN
        -- All authenticated users can read timezone config
        EXECUTE 'CREATE POLICY "timezone_config_select" ON public.timezone_config
          FOR SELECT TO authenticated
          USING (true)';

        -- Only admins can update timezone config
        EXECUTE 'CREATE POLICY "timezone_config_admin" ON public.timezone_config
          FOR ALL TO authenticated
          USING (public.is_admin())
          WITH CHECK (public.is_admin())';
    END IF;
END $$;

-- SUPPORTED TIMEZONES POLICIES (CONDITIONAL)
-- ===========================================

-- Only create policies if table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'supported_timezones') THEN
        -- All users can read active supported timezones
        EXECUTE 'CREATE POLICY "supported_timezones_select" ON public.supported_timezones
          FOR SELECT TO authenticated, anon
          USING (is_active = true)';

        -- Only admins can manage supported timezones
        EXECUTE 'CREATE POLICY "supported_timezones_admin" ON public.supported_timezones
          FOR ALL TO authenticated
          USING (public.is_admin())
          WITH CHECK (public.is_admin())';
    END IF;
END $$;

-- AUDIT LOG POLICIES (CONDITIONAL)
-- =================================

-- Only create policies if table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'audit_log') THEN
        -- Users can read their own audit records
        EXECUTE 'CREATE POLICY "audit_log_select_own" ON public.audit_log
          FOR SELECT TO authenticated
          USING (
            public.is_owner(user_id)
            OR public.is_admin()
          )';

        -- System can insert audit records (via SECURITY DEFINER functions)
        EXECUTE 'CREATE POLICY "audit_log_insert_system" ON public.audit_log
          FOR INSERT TO authenticated
          WITH CHECK (true)';  -- Controlled by SECURITY DEFINER functions
    END IF;
END $$;

-- ============================================================================
-- STEP 5: Create performance indexes for RLS optimization
-- ============================================================================

-- Indexes for users table RLS performance
CREATE INDEX IF NOT EXISTS idx_users_id_role_approved ON public.users(id, role, approved);
CREATE INDEX IF NOT EXISTS idx_users_approved ON public.users(approved) WHERE approved = true;

-- Indexes for tasks table RLS performance  
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON public.tasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_by ON public.tasks(assigned_by);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to_by ON public.tasks(assigned_to, assigned_by);

-- Indexes for notifications RLS performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);

-- Indexes for task_sessions RLS performance
CREATE INDEX IF NOT EXISTS idx_task_sessions_user_id ON public.task_sessions(user_id);

-- Indexes for audit_log RLS performance
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON public.audit_log(user_id);

-- ============================================================================
-- STEP 6: Add comments for documentation
-- ============================================================================

COMMENT ON FUNCTION public.is_admin() IS 'Security definer function to check admin status without RLS recursion';
COMMENT ON FUNCTION public.is_owner(UUID) IS 'Security definer function to check record ownership';

-- ============================================================================
-- STEP 7: Create schema migrations table if it doesn't exist
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.schema_migrations (
  version TEXT PRIMARY KEY,
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Migration completion marker
INSERT INTO public.schema_migrations (version, applied_at)
VALUES ('013_fix_all_rls_policies', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();
